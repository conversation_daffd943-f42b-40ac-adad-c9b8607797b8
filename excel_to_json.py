# excel_to_json.py
import pandas as pd
import json
import numpy as np
import sys

def clean_value(value):
    """处理Pandas读取时可能的NaN值，并正确转换数值类型。"""
    if pd.isna(value) or value is None:
        return None
    if isinstance(value, (int, float, np.number)):
        if isinstance(value, np.integer):
            return int(value)
        if isinstance(value, np.floating) and value.is_integer():
            return int(value)
        return float(value)
    return str(value)

def convert_excel_to_json(excel_path="menu_output.xlsx", json_path="menu_for_upload.json"):
    """将多工作表Excel文件转换回层级结构的JSON文件。"""
    print(f"正在读取 Excel 文件: {excel_path}")
    try:
        sheets = pd.read_excel(excel_path, sheet_name=None)
    except FileNotFoundError:
        print(f"错误: 文件未找到 {excel_path}", file=sys.stderr)
        return False
    except Exception as e:
        print(f"读取Excel文件时发生错误: {e}", file=sys.stderr)
        return False

    # --- 1. 创建 Human_ID -> DB_ID 映射表 ---
    human_to_db = {}
    try:
        for _, row in sheets['Options'].iterrows():
            if clean_value(row.get('DB_ID')):
                human_to_db[row['Option_ID']] = clean_value(row['DB_ID'])
        for _, row in sheets['Addons'].iterrows():
            if clean_value(row.get('DB_ID')):
                human_to_db[row['Addon_ID']] = clean_value(row['DB_ID'])
        for _, row in sheets['Categories'].iterrows():
            if clean_value(row.get('DB_ID')):
                human_to_db[row['Category_ID']] = clean_value(row['DB_ID'])
    except KeyError as e:
        print(f"错误: Excel文件中缺少必要的工作表: {e}", file=sys.stderr)
        return False
    
    # --- 2. 重建JSON结构 ---
    output = {}

    # Restaurant_Info
    info_df = sheets['Restaurant_Info'].set_index('Key')['Value']
    output['_id'] = clean_value(info_df.get('DB_ID'))
    output['name'] = clean_value(info_df.get('name'))
    output['image'] = clean_value(info_df.get('image'))
    output['logo'] = clean_value(info_df.get('logo'))
    output['address'] = clean_value(info_df.get('address'))
    output['deliveryTime'] = clean_value(info_df.get('deliveryTime'))
    output['minimumOrder'] = clean_value(info_df.get('minimumOrder'))
    output['tax'] = clean_value(info_df.get('tax'))
    output['shopType'] = clean_value(info_df.get('shopType'))
    output['phone'] = clean_value(info_df.get('phone'))
    output['restaurantBrand'] = clean_value(info_df.get('restaurantBrand'))
    output['restaurantBrandId'] = clean_value(info_df.get('restaurantBrandId'))
    
    cuisines_str = clean_value(info_df.get('cuisines'))
    output['cuisines'] = [c.strip() for c in cuisines_str.split(',') if c.strip()] if cuisines_str else []
    
    tags_str = clean_value(info_df.get('tags'))
    output['tags'] = [t.strip() for t in tags_str.split(',') if t.strip()] if tags_str else []

    # Opening_Times
    output['openingTimes'] = []
    times_df = sheets['Opening_Times']
    for day, group in times_df.groupby('Day_of_Week'):
        day_times = []
        for _, row in group.iterrows():
            start, end = clean_value(row['Start_Time (HH:MM)']), clean_value(row['End_Time (HH:MM)'])
            if start and end:
                day_times.append({'startTime': [start], 'endTime': [end]})
        output['openingTimes'].append({'day': day, 'times': day_times})

    # Options, Addons
    output['options'] = [
        {k: v for k, v in {
            '_id': clean_value(row.get('DB_ID')), 'title': row['Title'],
            'description': clean_value(row.get('Description')) or "", 'price': float(row['Price'])
        }.items() if v is not None} for _, row in sheets['Options'].iterrows()
    ]
    output['addons'] = [
        {k: v for k, v in {
            '_id': clean_value(row.get('DB_ID')), 'title': row['Title'],
            'description': clean_value(row.get('Description')) or "",
            'quantityMinimum': int(row['Min_Quantity']), 'quantityMaximum': int(row['Max_Quantity']),
            'options': [human_to_db.get(opt_id.strip()) for opt_id in str(row.get('Applicable_Option_IDs', '')).split(',') if opt_id.strip() and human_to_db.get(opt_id.strip())]
        }.items() if v is not None} for _, row in sheets['Addons'].iterrows()
    ]

    # Categories, Foods, Variations
    output['categories'] = []
    cat_df = sheets['Categories']
    food_df = sheets['Foods_and_Variations']
    
    for _, cat_row in cat_df.iterrows():
        category = {
            '_id': clean_value(cat_row.get('DB_ID')),
            'title': cat_row['Title'],
            'foods': []
        }
        
        foods_in_cat = food_df[food_df['Category_ID'].astype(str) == str(cat_row['Category_ID'])]
        for food_id, group in foods_in_cat.groupby('Food_ID'):
            first_row = group.iloc[0]
            
            food_description = clean_value(first_row.get('Food_Description'))
            if food_description is None:
                food_description = ""

            food = {
                '_id': clean_value(first_row.get('DB_ID (Food)')),
                'title': first_row['Food_Title'],
                'description': food_description,
                'image': clean_value(first_row.get('Food_Image_URL')),
                'isActive': bool(first_row['Is_Active']),
                'variations': []
            }

            for index, var_row in group.iterrows():
                try:
                    price = float(var_row['Price'])
                    discounted_raw = clean_value(var_row.get('Discounted_Price'))
                    discounted = float(discounted_raw) if discounted_raw is not None else None

                    variation = {
                        '_id': clean_value(var_row.get('DB_ID (Variation)')),
                        'title': var_row['Variation_Title'],
                        'price': price,
                        'discounted': discounted,
                        'addons': [human_to_db.get(add_id.strip()) for add_id in str(var_row.get('Applicable_Addon_IDs', '')).split(',') if add_id.strip() and human_to_db.get(add_id.strip())]
                    }
                    food['variations'].append({k: v for k, v in variation.items() if v is not None})
                
                except (ValueError, TypeError) as e:
                    print("\n❌ 错误：Excel文件中的数据格式不正确！", file=sys.stderr)
                    print(f"   - 工作表: Foods_and_Variations", file=sys.stderr)
                    print(f"   - 菜品  : {first_row['Food_Title']}", file=sys.stderr)
                    print(f"   - 规格  : {var_row['Variation_Title']}", file=sys.stderr)
                    print(f"   - 问题列: 'Price' 或 'Discounted_Price' 应该只包含数字或为空。", file=sys.stderr)
                    print(f"   - 错误详情: {e}", file=sys.stderr)
                    return False
            
            category['foods'].append(food)
        output['categories'].append(category)

    # === MODIFICATION STARTS HERE ===
    # 在保存之前，将所有组装好的数据包裹在一个 "restaurant" 键中
    output_data_wrapped = {"restaurant": output}

    with open(json_path, 'w', encoding='utf-8') as f:
        json.dump(output_data_wrapped, f, indent=2, ensure_ascii=False)
    # === MODIFICATION ENDS HERE ===
        
    print(f"✅ 成功将Excel数据转换为JSON文件: {json_path}")
    return True