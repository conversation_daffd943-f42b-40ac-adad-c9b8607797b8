# main.py
import argparse
import json
import sys

from api_client import ApiClient
from downloader import download_data
from json_to_excel import convert_json_to_excel
from excel_to_json import convert_excel_to_json
from uploader import Uploader

def load_config(path="config.json"):
    """Loads the configuration file."""
    try:
        with open(path, 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"Error: Configuration file '{path}' not found.", file=sys.stderr)
        sys.exit(1)
    except json.JSONDecodeError:
        print(f"Error: Configuration file '{path}' is not formatted correctly.", file=sys.stderr)
        sys.exit(1)

def main():
    parser = argparse.ArgumentParser(description="A tool for managing restaurant menus between a database and Excel files.")
    
    # Sub-parsers for commands to make --id required for specific commands
    subparsers = parser.add_subparsers(dest="command", required=True, help="Available commands")

    # Command: download
    parser_download = subparsers.add_parser("download", help="Download menu from the server to a JSON file.")
    parser_download.add_argument("--id", dest="restaurant_id", required=True, help="The database ID of the restaurant (required).")
    parser_download.add_argument("--json-file", default="menu.json", help="Output JSON file name.")

    # Command: upload
    parser_upload = subparsers.add_parser("upload", help="Upload a JSON menu file to the server.")
    parser_upload.add_argument("--id", dest="restaurant_id", required=True, help="The database ID of the restaurant (required).")
    parser_upload.add_argument("--json-file", required=True, help="Input JSON file to upload (required).")
    
    # Command: json2excel
    parser_j2e = subparsers.add_parser("json2excel", help="Convert a JSON menu file to an Excel file.")
    parser_j2e.add_argument("--json-file", required=True, help="Input JSON file (required).")
    parser_j2e.add_argument("--excel-file", default="menu.xlsx", help="Output Excel file name.")

    # Command: excel2json
    parser_e2j = subparsers.add_parser("excel2json", help="Convert an Excel menu file to a JSON file.")
    parser_e2j.add_argument("--excel-file", required=True, help="Input Excel file (required).")
    parser_e2j.add_argument("--json-file", default="menu.json", help="Output JSON file name.")

    args = parser.parse_args()
    config = load_config()
    
    client = None
    if args.command in ['download', 'upload']:
        print("Initializing API client and logging in...")
        client = ApiClient(config['graphql_endpoint'])
        if not client.login(config['admin_email'], config['admin_password']):
            print("Login failed. Exiting.", file=sys.stderr)
            sys.exit(1)
        print("-" * 20)

    # Execute command
    if args.command == 'download':
        print(f"*** Operating on Restaurant ID: {args.restaurant_id} ***")
        download_data(client, args.restaurant_id, args.json_file)
    
    elif args.command == 'upload':
        print(f"*** Operating on Restaurant ID: {args.restaurant_id} ***")
        uploader = Uploader(client, args.restaurant_id)
        uploader.upload_data(args.json_file)
    
    elif args.command == 'json2excel':
        convert_json_to_excel(args.json_file, args.excel_file)

    elif args.command == 'excel2json':
        convert_excel_to_json(args.excel_file, args.json_file)

if __name__ == "__main__":
    main()