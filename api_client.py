# api_client.py
import requests
import sys
from typing import Dict, Any

class ApiClient:
    """
    一个通用的GraphQL API客户端，负责认证和执行请求。
    """
    def __init__(self, graphql_url: str):
        self.graphql_url = graphql_url
        self.token = None
        self.session = requests.Session()

    def login(self, email: str, password: str) -> bool:
        """
        使用 ownerLogin 获取权限并存储令牌。
        """
        print(f"正在尝试以 {email} 身份登录...")
        mutation = """
        mutation OwnerLogin($email: String!, $password: String!) {
            ownerLogin(email: $email, password: $password) {
                userId
                token
                email
                userType
            }
        }
        """
        variables = {"email": email, "password": password}
        
        # 登录不使用认证头
        payload = {'query': mutation, 'variables': variables}
        try:
            response = self.session.post(self.graphql_url, json=payload)
            response.raise_for_status()
            result = response.json()

            if 'errors' in result:
                error_msg = result['errors'][0]['message']
                print(f"登录失败: {error_msg}", file=sys.stderr)
                return False
            
            login_data = result.get('data', {}).get('ownerLogin')
            if not login_data:
                print("登录失败: 响应中未包含 'ownerLogin' 数据。", file=sys.stderr)
                return False

            self.token = login_data['token']
            print(f"✅ 登录成功！用户类型: {login_data.get('userType')}")
            return True

        except requests.exceptions.RequestException as e:
            print(f"网络请求失败: {e}", file=sys.stderr)
            return False
        except json.JSONDecodeError:
            print(f"无法解析服务器响应: {response.text}", file=sys.stderr)
            return False

    def execute(self, query: str, variables: Dict = None) -> Dict[str, Any]:
        """
        执行一个GraphQL查询或变更，自动附加认证令牌。
        """
        if not self.token:
            raise Exception("客户端未登录。请在执行操作前调用 login()。")

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.token}"
        }
        payload = {'query': query, 'variables': variables or {}}

        try:
            response = self.session.post(self.graphql_url, json=payload, headers=headers)
            response.raise_for_status()
            result = response.json()

            if 'errors' in result:
                print(f"GraphQL Error:\n{result['errors']}", file=sys.stderr)
                return None
            
            return result.get('data')
        
        except requests.exceptions.RequestException as e:
            print(f"网络请求失败: {e}", file=sys.stderr)
            return None
        except Exception as e:
            print(f"执行GraphQL时发生未知错误: {e}", file=sys.stderr)
            return None