# uploader.py
import json
import sys
from copy import deepcopy
from api_client import ApiClient

class Uploader:
    def __init__(self, api_client: ApiClient, restaurant_id: str):
        self.api_client = api_client
        self.restaurant_id = restaurant_id

    def _sync_options(self, options: list) -> list:
        """同步Options，处理创建和更新。"""
        print("正在同步 Options...")
        options_to_create = []
        options_to_update = []
        title_to_new_id_map = {}

        for opt in options:
            if '_id' in opt:
                options_to_update.append(opt)
            else:
                options_to_create.append({"title": opt["title"], "description": opt.get("description", ""), "price": opt["price"]})

        # 批量创建新 Options
        if options_to_create:
            print(f"检测到 {len(options_to_create)} 个新 Options，正在创建...")
            mutation = "mutation CreateOptions($input: CreateOptionInput!) { createOptions(optionInput: $input) { options { _id title } } }"
            variables = {"input": {"restaurant": self.restaurant_id, "options": options_to_create}}
            result = self.api_client.execute(mutation, variables)
            if result and result.get('createOptions'):
                created = result['createOptions'].get('options', [])
                print(f"✅ 成功创建 {len(created)} 个新 Options。")
                for item in created:
                    title_to_new_id_map[item['title']] = item['_id']
            else:
                raise Exception("创建新 Options 失败。")

        # 逐个更新已有 Options
        if options_to_update:
            print(f"正在更新 {len(options_to_update)} 个已有 Options...")
            mutation = "mutation EditOption($input: editOptionInput!) { editOption(optionInput: $input) { _id } }"
            for opt in options_to_update:
                variables = {"input": {"restaurant": self.restaurant_id, "options": opt}}
                if not self.api_client.execute(mutation, variables):
                    print(f"⚠️  更新 Option '{opt['title']}' 失败。")
        
        all_options_with_ids = deepcopy(options)
        for opt in all_options_with_ids:
            if '_id' not in opt and opt['title'] in title_to_new_id_map:
                opt['_id'] = title_to_new_id_map[opt['title']]
        
        print("Options 同步完成。")
        return all_options_with_ids

    def _sync_addons(self, addons: list) -> list:
        """同步Addons，处理创建和更新。"""
        print("正在同步 Addons...")
        addons_to_create = []
        addons_to_update = []
        title_to_new_id_map = {}

        for addon in addons:
            addon_input = {
                "title": addon["title"],
                "description": addon.get("description", ""),
                "quantityMinimum": addon["quantityMinimum"],
                "quantityMaximum": addon["quantityMaximum"],
                "options": addon.get("options", [])
            }
            if '_id' in addon:
                addon_input['_id'] = addon['_id']
                addons_to_update.append(addon_input)
            else:
                addons_to_create.append(addon_input)

        if addons_to_create:
            print(f"检测到 {len(addons_to_create)} 个新 Addons，正在创建...")
            mutation = "mutation CreateAddons($input: AddonInput!) { createAddons(addonInput: $input) { addons { _id title } } }"
            variables = {"input": {"restaurant": self.restaurant_id, "addons": addons_to_create}}
            result = self.api_client.execute(mutation, variables)
            if result and result.get('createAddons'):
                created = result['createAddons'].get('addons', [])
                print(f"✅ 成功创建 {len(created)} 个新 Addons。")
                for item in created:
                    title_to_new_id_map[item['title']] = item['_id']
            else:
                raise Exception("创建新 Addons 失败。")

        if addons_to_update:
            print(f"正在更新 {len(addons_to_update)} 个已有 Addons...")
            mutation = "mutation EditAddon($input: editAddonInput!) { editAddon(addonInput: $input) { _id } }"
            for addon_input in addons_to_update:
                variables = {"input": {"restaurant": self.restaurant_id, "addons": addon_input}}
                if not self.api_client.execute(mutation, variables):
                    print(f"⚠️  更新 Addon '{addon_input['title']}' 失败。")

        all_addons_with_ids = deepcopy(addons)
        for addon in all_addons_with_ids:
            if '_id' not in addon and addon['title'] in title_to_new_id_map:
                addon['_id'] = title_to_new_id_map[addon['title']]
        
        print("Addons 同步完成。")
        return all_addons_with_ids

    def _sync_categories_and_foods(self, categories: list):
        """同步Categories和Foods，处理创建和更新。"""
        print("正在同步 Categories 和 Foods...")
        all_categories_with_ids = deepcopy(categories)

        # Step 1: Sync all categories first
        for category in all_categories_with_ids:
            if '_id' not in category:
                print(f"正在创建新 Category: {category['title']}...")
                mutation = "mutation CreateCategory($input: CategoryInput!) { createCategory(category: $input) { _id } }"
                variables = {"input": {"restaurant": self.restaurant_id, "title": category['title']}}
                result = self.api_client.execute(mutation, variables)
                if result and result.get('createCategory'):
                    category['_id'] = result['createCategory']['_id']
                    print(f"✅ 成功创建 Category '{category['title']}'。")
                else:
                    raise Exception(f"创建新 Category '{category['title']}' 失败。")
            else:
                # 更新Category Title
                mutation = "mutation EditCategory($input: CategoryInput!) { editCategory(category: $input) { _id } }"
                variables = {"input": {"restaurant": self.restaurant_id, "title": category['title'], "_id": category['_id']}}
                if not self.api_client.execute(mutation, variables):
                     print(f"⚠️  更新 Category '{category['title']}' 失败。")

        # Step 2: Sync all foods, now that all categories have DB IDs
        print("正在同步 Foods 和 Variations...")
        for category in all_categories_with_ids:
            for food in category.get('foods', []):
                food_input = {
                    "restaurant": self.restaurant_id,
                    "category": category['_id'],
                    "title": food['title'],
                    "description": food.get('description', ''),
                    "image": food.get('image'),
                    "variations": food.get('variations', []) # Variations are part of the input
                }

                if '_id' in food:
                    food_input['_id'] = food['_id']
                    mutation = "mutation EditFood($input: FoodInput!) { editFood(foodInput: $input) { _id } }"
                    print(f"  - 正在更新 Food: {food['title']}...")
                else:
                    mutation = "mutation CreateFood($input: FoodInput!) { createFood(foodInput: $input) { _id } }"
                    print(f"  - 正在创建 Food: {food['title']}...")
                
                if not self.api_client.execute(mutation, {"input": food_input}):
                     raise Exception(f"处理 Food '{food['title']}' 失败。")

        print("Categories 和 Foods 同步完成。")


    def upload_data(self, json_path="menu_for_upload.json"):
        """主上传函数，按数据依赖顺序执行。"""
        print(f"正在从 {json_path} 加载数据以上传...")
        try:
            with open(json_path, 'r', encoding='utf-8') as f:
                wrapped_data = json.load(f)
            
            # 从 "restaurant" 键中解开数据
            data = wrapped_data.get("restaurant")
            if data is None:
                print(f"错误: 待上传的JSON文件 {json_path} 中缺少顶层 'restaurant' 键。", file=sys.stderr)
                return False

        except (IOError, json.JSONDecodeError) as e:
            print(f"错误: 无法读取或解析JSON文件 {json_path}: {e}", file=sys.stderr)
            return False

        try:
            # 1. 同步 Options (无依赖)
            all_options_with_ids = self._sync_options(data.get('options', []))
            
            # 将新ID回填到原始数据结构中，以便addons能正确引用
            data['options'] = all_options_with_ids

            # 2. 同步 Addons (依赖 Options)
            all_addons_with_ids = self._sync_addons(data.get('addons', []))
            data['addons'] = all_addons_with_ids

            # 3. 同步 Categories 和内嵌的 Foods (依赖 Addons)
            self._sync_categories_and_foods(data.get('categories', []))

            print("\n✅ 所有数据同步成功完成！")
            return True

        except Exception as e:
            print(f"\n❌ 上传过程中发生严重错误: {e}", file=sys.stderr)
            return False