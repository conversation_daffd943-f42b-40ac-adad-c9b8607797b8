# Restaurant Menu Management Tool

A command-line tool to synchronize restaurant menu data between a GraphQL database and Excel files.

## Features

-   **Download**: Pull the latest menu data from the server into a JSON file.
-   **Convert**: Transform data between JSON and a multi-sheet Excel format.
-   **Edit**: Manage all menu details (items, categories, variations, pricing) directly within the Excel file.
-   **Upload**: Synchronize changes from the Excel file back to the database.

## Setup (One-time only)

**1. Prerequisites**
   - [Python](https://www.python.org/downloads/) (version 3.7+).
   - Project files downloaded to your computer.

**2. Create a Virtual Environment**
   Open a terminal, navigate (`cd`) into the project directory, and run:
   ```bash
   python -m venv venv
   ```

**3. Install Dependencies**
   First, activate the virtual environment:
   -   On Windows: `.\venv\Scripts\activate`
   -   On macOS/Linux: `source venv/bin/activate`

   Then, install the required packages:
   ```bash
   pip install -r requirements.txt
   ```

**4. Configure `config.json`**
   Create a `config.json` file in the project root with the following content. **Do not add the restaurant ID here.**

   ```json
   {
     "graphql_endpoint": "YOUR_API_ENDPOINT_URL",
     "admin_email": "YOUR_ADMIN_EMAIL",
     "admin_password": "YOUR_ADMIN_PASSWORD"
   }
   ```
   Replace the placeholder values with your actual credentials.

---

## Usage Guide

All operations are performed via `main.py`. **Ensure your virtual environment is activated before running commands.**

### Workflow 1: Downloading and Editing a Menu

**Step 1: Download Menu Data**
   Run the `download` command, specifying the restaurant's database ID and an output file name.

   ```bash
   python main.py download --id "RESTAURANT_DB_ID_HERE" --json-file menu_from_db.json
   ```

**Step 2: Convert to Excel**
   Convert the downloaded JSON file into an editable Excel spreadsheet.

   ```bash
   python main.py json2excel --json-file menu_from_db.json --excel-file menu_latest.xlsx
   ```
   You can now open `menu_latest.xlsx` and edit the menu. Refer to the Excel Editing Guide.

### Workflow 2: Uploading a Modified Menu

**Step 1: Convert Excel to JSON**
   After editing and saving your spreadsheet (e.g., as `menu_edited.xlsx`), convert it back to the JSON format.

   ```bash
   python main.py excel2json --excel-file menu_edited.xlsx --json-file menu_for_upload.json
   ```

**Step 2: Upload to Server**
   Run the `upload` command, specifying the same restaurant ID and the JSON file to upload.

   ```bash
   python main.py upload --id "RESTAURANT_DB_ID_HERE" --json-file menu_for_upload.json
   ```
   The script will log in and synchronize the changes.

---

## Excel Editing Guide

-   **Do NOT modify `DB_ID` columns.** These are managed by the program.
-   For **new items** (options, addons, foods, etc.), create a **new, unique Human-Readable ID** (e.g., `Option_ID`) and **leave the `DB_ID` column blank**. This signals the program to create a new entry.
-   To **delete** an item, delete the entire row in the spreadsheet.
-   To **link** items (e.g., assigning addons to a food), use the Human-Readable IDs in the appropriate columns, separated by a single comma (`,`) for multiple values.