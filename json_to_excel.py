# json_to_excel.py
import json
import pandas as pd
import re
import sys

def generate_human_id(prefix, title, db_id):
    """根据标题和DB_ID生成一个稳定的人类可读ID。"""
    if not title:
        return f"{prefix}_{db_id[-6:]}"
    # 移除非字母数字字符，并转换为小写
    safe_title = re.sub(r'[^a-zA-Z0-9]', '_', title.lower())
    return f"{prefix}_{safe_title}_{db_id[-4:]}"

def convert_json_to_excel(json_path="menu_from_db.json", excel_path="menu_output.xlsx"):
    """将层级结构的JSON文件转换为扁平化的多工作表Excel文件。"""
    print(f"正在读取 JSON 文件: {json_path}")
    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            wrapped_data = json.load(f)
    except (IOError, json.JSONDecodeError) as e:
        print(f"错误: 无法读取或解析JSON文件 {json_path}: {e}", file=sys.stderr)
        return False

    # === MODIFICATION STARTS HERE ===
    # 从 "restaurant" 键中解开（提取）实际的餐厅数据
    data = wrapped_data.get("restaurant")
    if data is None:
        print(f"错误: JSON文件 {json_path} 中缺少顶层 'restaurant' 键。", file=sys.stderr)
        return False
    # === MODIFICATION ENDS HERE ===

    # --- 1. 创建 ID 映射表 ---
    db_to_human = {}
    for opt in data.get('options', []):
        db_to_human[opt['_id']] = generate_human_id('opt', opt['title'], opt['_id'])
    for add in data.get('addons', []):
        db_to_human[add['_id']] = generate_human_id('addon', add['title'], add['_id'])
    for cat in data.get('categories', []):
        db_to_human[cat['_id']] = generate_human_id('cat', cat['title'], cat['_id'])
        for food in cat.get('foods', []):
            db_to_human[food['_id']] = generate_human_id('food', food['title'], food['_id'])

    # --- 2. 处理每个工作表 ---
    writer = pd.ExcelWriter(excel_path, engine='openpyxl')

    # Restaurant_Info
    info_data = {
        'Key': ['DB_ID', 'Human_ID', 'name', 'image', 'logo', 'address', 'deliveryTime', 
                'minimumOrder', 'tax', 'shopType', 'cuisines', 'tags', 'phone', 
                'restaurantBrand', 'restaurantBrandId'],
        'Value': [
            data.get('_id'), generate_human_id('rest', data.get('name'), data.get('_id')),
            data.get('name'), data.get('image'), data.get('logo'), data.get('address'),
            data.get('deliveryTime'), data.get('minimumOrder'), data.get('tax'),
            data.get('shopType'), ', '.join(data.get('cuisines', [])) if data.get('cuisines') else '',
            ', '.join(data.get('tags', [])) if data.get('tags') else '',
            data.get('phone'),
            data.get('restaurantBrand'), data.get('restaurantBrandId')
        ]
    }
    pd.DataFrame(info_data).to_excel(writer, sheet_name='Restaurant_Info', index=False)

    # Opening_Times
    times_rows = []
    for day_info in data.get('openingTimes', []):
        day = day_info['day']
        if not day_info.get('times'):
            times_rows.append({'Day_of_Week': day, 'Start_Time (HH:MM)': '', 'End_Time (HH:MM)': ''})
        else:
            for timing in day_info['times']:
                # startTime和endTime是数组，通常只取第一个元素
                start = timing.get('startTime', [''])[0]
                end = timing.get('endTime', [''])[0]
                times_rows.append({'Day_of_Week': day, 'Start_Time (HH:MM)': start, 'End_Time (HH:MM)': end})
    pd.DataFrame(times_rows).to_excel(writer, sheet_name='Opening_Times', index=False)

    # Options, Addons, Categories
    pd.DataFrame([{
        'DB_ID': o['_id'], 'Option_ID': db_to_human.get(o['_id']), 'Title': o['title'],
        'Description': o.get('description'), 'Price': o['price']
    } for o in data.get('options', [])]).to_excel(writer, sheet_name='Options', index=False)

    pd.DataFrame([{
        'DB_ID': a['_id'], 'Addon_ID': db_to_human.get(a['_id']), 'Title': a['title'],
        'Description': a.get('description'), 'Min_Quantity': a['quantityMinimum'],
        'Max_Quantity': a['quantityMaximum'], 'Applicable_Option_IDs': ','.join([db_to_human.get(opt_id, opt_id) for opt_id in a.get('options', [])])
    } for a in data.get('addons', [])]).to_excel(writer, sheet_name='Addons', index=False)

    pd.DataFrame([{
        'DB_ID': c['_id'], 'Category_ID': db_to_human.get(c['_id']), 'Title': c['title']
    } for c in data.get('categories', [])]).to_excel(writer, sheet_name='Categories', index=False)

    # Foods_and_Variations
    food_rows = []
    for cat in data.get('categories', []):
        for food in cat.get('foods', []):
            for var in food.get('variations', []):
                food_rows.append({
                    'DB_ID (Food)': food['_id'],
                    'Food_ID': db_to_human.get(food['_id']),
                    'Category_ID': db_to_human.get(cat['_id']),
                    'Food_Title': food['title'],
                    'Food_Description': food.get('description'),
                    'Food_Image_URL': food.get('image'),
                    'Is_Active': food.get('isActive'),
                    'DB_ID (Variation)': var['_id'],
                    'Variation_Title': var['title'],
                    'Price': var['price'],
                    'Discounted_Price': var.get('discounted'),
                    'Applicable_Addon_IDs': ','.join([db_to_human.get(add_id, add_id) for add_id in var.get('addons', [])])
                })
    pd.DataFrame(food_rows).to_excel(writer, sheet_name='Foods_and_Variations', index=False)
    
    writer.close()
    print(f"成功将JSON数据转换为Excel文件: {excel_path}")
    return True