# downloader.py
import json
import sys
from api_client import ApiClient  # 引入ApiClient

# 精确查询 Excel 表格所需的所有字段，并处理可能为空的 'description' 字段
GQL_QUERY = """
query GetRestaurantMenuData($id: String!) {
  restaurant(id: $id) {
    _id
    name
    image
    logo
    address
    deliveryTime
    minimumOrder
    tax
    shopType
    cuisines
    tags
    phone
    restaurantBrand
    restaurantBrandId
    openingTimes {
      day
      times {
        startTime
        endTime
      }
    }
    options {
      _id
      title
      description
      price
    }
    addons {
      _id
      title
      description
      quantityMinimum
      quantityMaximum
      options
    }
    categories {
      _id
      title
      foods {
        _id
        title
        description
        image
        isActive
        variations {
          _id
          title
          price
          discounted
          addons
        }
      }
    }
  }
}
"""

def download_data(api_client: ApiClient, restaurant_id: str, output_path="menu_from_db.json"):
    """
    使用传入的ApiClient实例从服务器下载餐厅菜单数据。

    Args:
        api_client: 一个已经过认证的ApiClient实例。
        restaurant_id: 目标餐厅的数据库ID。
        output_path: 保存JSON文件的路径。
    
    Returns:
        bool: 操作是否成功。
    """
    print("正在从服务器下载数据...")
    
    variables = {"id": restaurant_id}
    # 使用注入的api_client实例来执行请求
    result = api_client.execute(GQL_QUERY, variables)

    if not result:
        print("错误：执行GraphQL查询失败，未从服务器获取到有效数据。", file=sys.stderr)
        return False
        
    restaurant_data = result.get('restaurant')
    if not restaurant_data:
        print(f"错误：未在返回数据中找到ID为 '{restaurant_id}' 的餐厅信息。", file=sys.stderr)
        return False

    try:
        # === MODIFICATION STARTS HERE ===
        # 将获取到的餐厅数据包裹在一个顶层的 "restaurant" 键中
        output_data_wrapped = {"restaurant": restaurant_data}
        
        with open(output_path, 'w', encoding='utf-8') as f:
            # 将被包裹后的完整结构写入文件
            json.dump(output_data_wrapped, f, indent=2, ensure_ascii=False)
        # === MODIFICATION ENDS HERE ===

    except IOError as e:
        print(f"错误：无法写入文件 {output_path}: {e}", file=sys.stderr)
        return False
    
    print(f"✅ 数据成功下载并保存到: {output_path}")
    return True